{"log": {"level": "warn"}, "dns": {"servers": [{"tag": "google", "address": "*******", "address_resolver": "local"}, {"tag": "google-ipv6", "address": "2001:4860:4860::8888", "address_resolver": "local"}, {"tag": "local", "address": "*********", "detour": "direct"}, {"tag": "local-ipv6", "address": "2400:3200::1", "detour": "direct"}], "rules": [{"outbound": "any", "server": "local"}], "final": "google"}, "inbounds": [{"type": "tun", "tag": "tun-in", "inet4_address": "**********/30", "inet6_address": "fdfe:dcba:9876::1/126", "auto_route": true, "strict_route": true, "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "vless", "tag": "proxy", "server": "************", "server_port": 443, "uuid": "d2d58ff0-ca06-4a12-8fe7-532f7b62c401", "tls": {"enabled": true, "server_name": "sgw.tiantui.top", "insecure": true, "alpn": ["h3", "h2", "http/1.1"]}, "transport": {"type": "ws", "path": "/?ed=2560", "headers": {}}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"protocol": "dns", "action": "hijack-dns"}, {"ip_is_private": true, "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}