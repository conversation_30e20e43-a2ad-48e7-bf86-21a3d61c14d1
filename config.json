{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "cloudflare", "address": "*******"}, {"tag": "local", "address": "*********", "detour": "direct"}], "rules": [{"domain_suffix": [".cn"], "server": "local"}], "final": "cloudflare"}, "inbounds": [{"type": "vless", "tag": "vless-ws-in", "listen": "::", "listen_port": 8080, "users": [{"name": "user1", "uuid": "bf000d23-0752-40b4-affe-68f7707a9661", "flow": ""}, {"name": "user2", "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "flow": ""}], "transport": {"type": "ws", "path": "/vless-ws", "headers": {"Host": "example.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}, "multiplex": {"enabled": true, "padding": false}}, {"type": "vless", "tag": "vless-ws-tls-in", "listen": "::", "listen_port": 443, "users": [{"name": "user1", "uuid": "bf000d23-0752-40b4-affe-68f7707a9661", "flow": ""}, {"name": "user2", "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "flow": ""}], "tls": {"enabled": true, "server_name": "example.com", "certificate_path": "/path/to/certificate.pem", "key_path": "/path/to/private.key", "alpn": ["h2", "http/1.1"], "min_version": "1.2", "max_version": "1.3"}, "transport": {"type": "ws", "path": "/vless-ws-tls", "headers": {"Host": "example.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}, "multiplex": {"enabled": true, "padding": false}}, {"type": "vless", "tag": "vless-ws-tls-acme-in", "listen": "::", "listen_port": 8443, "users": [{"name": "user1", "uuid": "bf000d23-0752-40b4-affe-68f7707a9661", "flow": ""}], "tls": {"enabled": true, "server_name": "your-domain.com", "alpn": ["h2", "http/1.1"], "acme": {"domain": ["your-domain.com"], "email": "<EMAIL>", "provider": "letsencrypt", "data_directory": "/etc/ssl/acme", "disable_http_challenge": false, "disable_tls_alpn_challenge": false}}, "transport": {"type": "ws", "path": "/vless-ws-tls-acme", "headers": {"Host": "your-domain.com"}, "max_early_data": 2048}}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}, {"domain_suffix": [".cn", ".中国"], "outbound": "direct"}, {"geoip": "cn", "outbound": "direct"}], "auto_detect_interface": true, "final": "direct"}}