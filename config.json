{"log": {"level": "warn"}, "dns": {"servers": [{"type": "tls", "tag": "google", "server": "*******", "server_port": 853, "detour": "proxy"}, {"type": "udp", "tag": "local", "server": "*********", "server_port": 53, "detour": "direct"}], "rules": [{"outbound": "direct", "server": "local"}], "final": "google"}, "inbounds": [{"type": "tun", "tag": "tun-in", "inet4_address": "**********/30", "inet6_address": "fdfe:dcba:9876::1/126", "auto_route": true, "strict_route": true, "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "vless", "tag": "proxy", "server": "************", "server_port": 443, "uuid": "d2d58ff0-ca06-4a12-8fe7-532f7b62c401", "network": "tcp", "tls": {"enabled": true, "server_name": "sgw.tiantui.top", "insecure": true, "alpn": ["h3", "h2", "http/1.1"]}, "transport": {"type": "ws", "path": "/?ed=2560", "headers": {}}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"protocol": "dns", "action": "hijack-dns"}, {"ip_is_private": true, "outbound": "direct"}, {"ip_cidr": ["*********/4", "ff00::/8"], "outbound": "block"}], "final": "proxy", "auto_detect_interface": true}}